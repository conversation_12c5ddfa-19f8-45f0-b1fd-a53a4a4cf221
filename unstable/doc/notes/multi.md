# Multi
Gruplar arasinda olculen parametrelerde fark var mi?
## Two Independent Groups Analysis
### Without Split
#### Request parameters
```
HDL	LDL	Cinsiyet	xPositive?
1	9	Erkek		Yes
2	10	Erkek		No
3	11	Erkek		Yes
4	12	Erkek		No
5	13	Kadin		Yes
6	14	Kadin		No
7	15	Kadin		Yes
8	16	Kadin		No
```
```json
{
    "variable_list": [
        [0, [1, 2, 3, 4, 5, 6, 7, 8]],
        [1, [9, 10, 11, 12, 13, 14, 15, 16]]
    ],
    "group_list": [
        [5, [1, 1, 1, 1, 2, 2, 2, 2]],
        [6, [1, 1, 1, 1, 0, 0, 0, 0]]
    ],
    "split_list": []
}
```
#### Analysis Logic
```R
two_groups_independent(variable_list, group_list) {
	# Comparison HDL by cinsiyet
	# Comparison HDL by x positive?
	# Comparison LDL by cinsiyet
	# Comparison LDL by x positive?
}
```
- <PERSON>rk<PERSON>ler ve kadinlar arasinda HDL'lerde fark var mi?
- x pozitif olanlar ile x negatif olanlar arasinda HDL'lerde fark var mi?

- Erkekler ve kadinlar arasinda LDL'lerde fark var mi?
- x pozitif olanlar ile x negatif olanlar arasinda LDL'lerde fark var mi?
#### Response
```json
[
    {
        "id": 0,
        "common": {
            "is_normal": true,
            "descriptives": {
                "mean": 4.5,
                "sd": 2.4495,
                "median": 4.5,
                "min": 1,
                "max": 8
            }
        },
        "factors": [
            {
                "id": 5,
                "common": {
                    "method": "independent_t_test",
                    "cluster_method": {},
                    "t": -4.3818,
                    "p": 0.0047
                },
                "sub": [
                    {
                        "ind": 1,
                        "mean": 2.5,
                        "sd": 1.291,
                        "median": 2.5,
                        "min": 1,
                        "max": 4,
                        "cluster": {}
                    },
                    {
                        "ind": 2,
                        "mean": 6.5,
                        "sd": 1.291,
                        "median": 6.5,
                        "min": 5,
                        "max": 8,
                        "cluster": {}
                    }
                ]
            },
            {
                "id": 6,
                "common": {
                    "method": "independent_t_test",
                    "cluster_method": {},
                    "t": 4.3818,
                    "p": 0.0047
                },
                "sub": [
                    {
                        "ind": 1,
                        "mean": 6.5,
                        "sd": 1.291,
                        "median": 6.5,
                        "min": 5,
                        "max": 8,
                        "cluster": {}
                    },
                    {
                        "ind": 0,
                        "mean": 2.5,
                        "sd": 1.291,
                        "median": 2.5,
                        "min": 1,
                        "max": 4,
                        "cluster": {}
                    }
                ]
            }
        ]
    },
    {
        "id": 1,
        "common": {
            "is_normal": true,
            "descriptives": {
                "mean": 12.5,
                "sd": 2.4495,
                "median": 12.5,
                "min": 9,
                "max": 16
            }
        },
        "factors": [
            {
                "id": 5,
                "common": {
                    "method": "independent_t_test",
                    "cluster_method": {},
                    "t": -4.3818,
                    "p": 0.0047
                },
                "sub": [
                    {
                        "ind": 1,
                        "mean": 10.5,
                        "sd": 1.291,
                        "median": 10.5,
                        "min": 9,
                        "max": 12,
                        "cluster": {}
                    },
                    {
                        "ind": 2,
                        "mean": 14.5,
                        "sd": 1.291,
                        "median": 14.5,
                        "min": 13,
                        "max": 16,
                        "cluster": {}
                    }
                ]
            },
            {
                "id": 6,
                "common": {
                    "method": "independent_t_test",
                    "cluster_method": {},
                    "t": 4.3818,
                    "p": 0.0047
                },
                "sub": [
                    {
                        "ind": 1,
                        "mean": 14.5,
                        "sd": 1.291,
                        "median": 14.5,
                        "min": 13,
                        "max": 16,
                        "cluster": {}
                    },
                    {
                        "ind": 0,
                        "mean": 10.5,
                        "sd": 1.291,
                        "median": 10.5,
                        "min": 9,
                        "max": 12,
                        "cluster": {}
                    }
                ]
            }
        ]
    }
]
```
### With Split
#### Request Parameters
```
HDL	LDL	Cinsiyet	X Positive?		Y Positive?
1	9	Erkek		Yes				Yes
2	10	Erkek		Yes				No
3	11	Erkek		Yes				Yes
4	12	Erkek		Yes				No
5	13	Erkek		No				Yes
6	14	Erkek		No				No
7	15	Erkek		No				Yes
8	16	Kadin		No				No
9	17	Kadin		No				Yes
10	18	Kadin		No				No
11	19	Kadin		Yes				Yes
12	20	Kadin		Yes				No
13	21	Kadin		Yes				Yes
14	22	Kadin		Yes				No
```
```json
{
    "variable_list": [
        [0, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]], // HDL
        [1, [9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]] //LDL
    ],
    "group_list": [
        [5, [1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2]], // Cinsiyet
        [6, [1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1]]	// xPositive?
    ],
    "split_list": [9, [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1]] //yPositive?
}
```
#### Analysis Logic
```R
two_groups_independent(variable_list, group_list, split_list) {
	# Comparison HDL by cinsiyet by y positive?
	# Comparison HDL by x positive? by y positive?
	# Comparison LDL by cinsiyet by y positive?
	# Comparison LDL by x positive? by y positive?
}
```
- Y Pozitif olanlarda Erkekler ve kadinlar arasinda HDL'lerde fark var mi?
- Y Negatif olanlarda Erkekler ve kadinlar arasinda HDL'lerde fark var mi?

- Y Pozitif olanlarda x pozitif olanlar ile x negatif olanlar arasinda HDL'lerde fark var mi?
- Y Negatif olanlarda x pozitif olanlar ile x negatif olanlar arasinda HDL'lerde fark var mi?

- Y Pozitif olanlarda Erkekler ve kadinlar arasinda LDL'lerde fark var mi?
- Y Negatif olanlarda Erkekler ve kadinlar arasinda LDL'lerde fark var mi?

- Y Pozitif olanlarda x pozitif olanlar ile x negatif olanlar arasinda LDL'lerde fark var mi?
- Y Negatif olanlarda x pozitif olanlar ile x negatif olanlar arasinda LDL'lerde fark var mi?
#### Response
```json
{
    "split_id": 0,
    "sub": [
        {
            "ind": 1,
            "stats": [
                {
                    "id": 7,
                    "common": {
                        "is_normal": true,
                        "descriptives": {
                            "mean": 4.5,
                            "std": 2.4495,
                            "median": 4.5,
                            "min": 1,
                            "max": 8
                        }
                    },
                    "factors": [
                        {
                            "id": 5,
                            "common": {
                                "method": "independent_t_test",
                                "t": 1.1882,
                                "p": 0.2797,
                                "ci": [
                                    -2.1188,
                                    6.1188
                                ]
                            },
                            "sub": [
                                {
                                    "ind": 0,
                                    "mean": 2.5,
                                    "sd": 1.291,
                                    "median": 2.5,
                                    "min": 1,
                                    "max": 4,
                                    "pairs": [
                                        {
                                            "ind": 1
                                        },
                                        {
                                            "ind": 2,
                                            "p": 0.5
                                        }
                                    ]
                                },
                                {
                                    "ind": 1,
                                    "mean": 2.5,
                                    "sd": 1.291,
                                    "median": 2.5,
                                    "min": 1,
                                    "max": 4,
                                    "pairs": [
                                        {
                                            "ind": 1,
                                            "p": 0.5
                                        },
                                        {
                                            "ind": 2
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "ind": 2,
            "stats": [
                {
                    "id": 7,
                    "common": {
                        "is_normal": true,
                        "descriptives": {
                            "mean": 4.5,
                            "std": 2.4495,
                            "median": 4.5,
                            "min": 1,
                            "max": 8
                        }
                    },
                    "factors": [
                        {
                            "id": 5,
                            "common": {
                                "method": "independent_t_test",
                                "t": 1.1882,
                                "p": 0.2797,
                                "ci": [
                                    -2.1188,
                                    6.1188
                                ]
                            },
                            "sub": [
                                {
                                    "ind": 0,
                                    "mean": 2.5,
                                    "sd": 1.291,
                                    "median": 2.5,
                                    "min": 1,
                                    "max": 4,
                                    "pairs": [
                                        {
                                            "ind": 1
                                        },
                                        {
                                            "ind": 2,
                                            "p": 0.5
                                        }
                                    ]
                                },
                                {
                                    "ind": 1,
                                    "mean": 2.5,
                                    "sd": 1.291,
                                    "median": 2.5,
                                    "min": 1,
                                    "max": 4,
                                    "pairs": [
                                        {
                                            "ind": 1,
                                            "p": 0.5
                                        },
                                        {
                                            "ind": 2
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
}
```
## Three Plus Groups Analysis
### Without Split
#### Request Parameters
```
HDL	LDL	Anxiety Level	Obesity Level
1	9	Low				High
2	10	Low				High
3	11	Low				High
4	12	Medium			Low
5	13	Medium			Low
6	14	Medium			Low
7	15	High			Low
8	16	High			Medium
9	17	High			Medium
10	18	High			Medium
11  19	Low             High
12  20  Low             High
13	21	Low				High
14	22	Low				High
15	23	Medium			Low
16	24	Medium			Low
17  25	Medium			Low
18  26	Medium			Medium
19  27	High			Medium
20  28	High			Medium
21  29	High			Medium
```
### With Split
#### Request Parameters
```
HDL	LDL	Cinsiyet	Anxiety Level	Obesity Level
1	9	Erkek		Low				High
2	10	Erkek		Low				High
3	11	Erkek		Low				High
4	12	Erkek		Medium			Low
5	13	Erkek		Medium			Low
6	14	Erkek		Medium			Low
7	15	Erkek		High			Low
8	16	Erkek		High			Medium
9	17	Erkek		High			Medium
10	18	Erkek		High			Medium
11  19  Kadin       Low             High
12  20  Kadin       Low             High
13	21	Kadin		Low				High
14	22	Kadin		Low				High
15	23	Kadin		Medium			Low
16	24	Kadin		Medium			Low
17  25	Kadin		Medium			Low
18  26	Kadin		Medium			Medium
19  27	Kadin		High			Medium
20  28	Kadin       High			Medium
21  29	Kadin		High			Medium
```
```json
{
    "variable_list": [
        [0, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]], // HDL
        [1, [9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]] // LDL
    ],
    "group_list": [
        [6, [1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3]], // Anxiety Level
        [7, [3, 3, 3, 1, 1, 1, 1, 2, 2, 2, 3, 3, 3, 3, 1, 1, 1, 2, 2, 2, 2]] // Obesity Level
    ],
    "split_list": [5, [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]] // Cinsiyet]
}
```
#### Analysis Logic
```R
three_plus_groups_independent(variable_list, group_list, split_list) {
    # Comparison HDL by anxiety level by obesity level by cinsiyet
    # Comparison LDL by anxiety level by obesity level by cinsiyet
}
```
Erkeklerde; Dusuk, orta, yuksek anksiyete seviyelerine sahip bireyler arasinda HDL degerleri arasinda fark var mi?
Erkeklerde; Dusuk, orta, yuksek anksiyete seviyelerine sahip bireyler arasinda LDL degerleri arasinda fark var mi?
Erkeklerde; Dusuk, orta, yuksek obezite seviyelerine sahip bireyler arasinda HDL degerleri arasinda fark var mi?
Erkeklerde; Dusuk, orta, yuksek obezite seviyelerine sahip bireyler arasinda LDL degerleri arasinda fark var mi?

Kadinlarda; Dusuk, orta, yuksek anksiyete seviyelerine sahip bireyler arasinda HDL degerleri arasinda fark var mi?
Kadinlarda; Dusuk, orta, yuksek anksiyete seviyelerine sahip bireyler arasinda LDL degerleri arasinda fark var mi?
Kadinlarda; Dusuk, orta, yuksek obezite seviyelerine sahip bireyler arasinda HDL degerleri arasinda fark var mi?
Kadinlarda; Dusuk, orta, yuksek obezite seviyelerine sahip bireyler arasinda LDL degerleri arasinda fark var mi?