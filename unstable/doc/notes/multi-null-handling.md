# R: Multi: Null Yonetimi
## Grup Degiskelerinde Null
| group1 | group2 | param1 |
|--------|--------|--------|
| 1      | null   | 1      |
| 1      | null   | 2      |
| 1      | null   | 3      |
| 1      | null   | 4      |
| 1      | null   | 5      |
| 1      | null   | 6      |
| 2      | 1      | 7      |
| 2      | 1      | 8      |
| 2      | 1      | 9      |
| 2      | 2      | 10     |
| 2      | 2      | 11     |
| 2      | 2      | 12     |

Onceki senaryolarda grup degiskeni icerisinde null gozlem gelemiyordu, artik gelecek. 
Cunku grup2 icerisinde bir alt grup daha var.
Ornegin kadinlar icerisindeki sigara icenler ve icmeyenler. Splitteki mantik ile ayni.
Dolayisiyla bu gruba karsilik gelen param1 sutununda yalnizca grup2 icin olculen veri secilmeli. 

## Tersi Durumda Olasi Senaryo: Variable'da Null
| group1 | param1 |
|--------|--------|
| 1      | null   |
| 1      | null   |
| 1      | null   |
| 1      | null   |
| 2      | 2      |
| 2      | 3      |
| 2      | 4      |
| 2      | 5      |
| 3      | 6      |
| 3      | 7      |
| 3      | 8      |
| 3      | 9      |

Bu senaryoda da param1'de verisi olmadigi icin 1. Grup analize dahil edilmez. Iki gruplu karsilastirma(t-test || MW) yapilir.
Kisaca, multi endpointi iki tarafli bir null mekanizmasina sahip olmali.

## Onemli Not
Parametrelerde olculen verileri gruplara gore karsilastirma yaparken filtreleme islemi yapsak da olculen verinin kendisini silmiyoruz. 
Dolayisiyla gruplardan bagimsiz olarak degiskenin tanimlayici istatistiklerini response'da common icerisinde verirken mutlaka bu filtreleme olmaksizin vermeliyiz.

# Grupta Null Handling
Id: 5 olan grup degiskeninde, 3. grup yok dolayisiyla null ile doldurulmus. Bu degisken icin 2 gruplu analiz(t-test, MW) yapilir.
Id: 6 olan grup degiskeninde ise tum gruplar tam, dolayisiyla 3 gruplu analiz(Anova, Kruskall yapilmalidir.)

## Request
```json
{
    "variable_list": [
        [0, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]],
        [1, [15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]]
    ],
    "group_list": [
        [5, [1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, null, null, null, null, null, null, null]],
        [6, [1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3]]
    ],
    "split_list": []
}
```

## Response
```json
{
    "variable_list": [
        [0, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]],
        [1, [15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]]
    ],
    "group_list": [
        [5, [1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, null, null, null, null, null, null, null]],
        [6, [1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3]]
    ],
    "split_list": []
}
```

# Null Handling Sonucunda Yeterli Grup Kalmazsa?
Analiz o grup icin yapilmaz, bir sonraki grup degiskenine gecilir. Ornegin;
## Request
```json
{
    "variable_list": [
        [0, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]],
        [1, [15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]]
    ],
    "group_list": [
        [5, [1, 1, 1, 1, 1, 1, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null]],
        [6, [1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3]]
    ],
    "split_list": []
}
```

## Response
```json
[
    {
        "id": 0,
        "common": {
            "is_normal": true,
            "descriptives": {
                "mean": 11,
                "sd": 6.2048,
                "median": 11,
                "min": 1,
                "max": 21
            }
        },
        "factors": [
            {
                "id": 6,
                "common": {
                    "method": "one_way_anova",
                    "cluster_method": "Tukey",
                    "t": 73.5,
                    "p": 2.1883e-09
                },
                "sub": [
                    {
                        "ind": 1,
                        "mean": 4,
                        "sd": 2.1602,
                        "median": 4,
                        "min": 1,
                        "max": 7,
                        "cluster": "c"
                    },
                    {
                        "ind": 2,
                        "mean": 11,
                        "sd": 2.1602,
                        "median": 11,
                        "min": 8,
                        "max": 14,
                        "cluster": "b"
                    },
                    {
                        "ind": 3,
                        "mean": 18,
                        "sd": 2.1602,
                        "median": 18,
                        "min": 15,
                        "max": 21,
                        "cluster": "a"
                    }
                ]
            }
        ]
    },
    {
        "id": 1,
        "common": {
            "is_normal": true,
            "descriptives": {
                "mean": 25,
                "sd": 6.2048,
                "median": 25,
                "min": 15,
                "max": 35
            }
        },
        "factors": [
            {
                "id": 6,
                "common": {
                    "method": "one_way_anova",
                    "cluster_method": "Tukey",
                    "t": 73.5,
                    "p": 2.1883e-09
                },
                "sub": [
                    {
                        "ind": 1,
                        "mean": 18,
                        "sd": 2.1602,
                        "median": 18,
                        "min": 15,
                        "max": 21,
                        "cluster": "c"
                    },
                    {
                        "ind": 2,
                        "mean": 25,
                        "sd": 2.1602,
                        "median": 25,
                        "min": 22,
                        "max": 28,
                        "cluster": "b"
                    },
                    {
                        "ind": 3,
                        "mean": 32,
                        "sd": 2.1602,
                        "median": 32,
                        "min": 29,
                        "max": 35,
                        "cluster": "a"
                    }
                ]
            }
        ]
    }
]
```

# Yukaridaki Her Iki Senaryo Bir Arada Olursa?
## Request
```json
{
  "variable_list": [
      [0, [null, null, null, null, null, null, null, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]],
      [1, [15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]]
  ],
  "group_list": [
      [5, [1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, null, null, null, null, null, null, null]],
      [6, [1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3]]
  ],
  "split_list": []
}
```

## Response
```json
[
    {
        "id": 0,
        "common": {
            "is_normal": true,
            "descriptives": {
                "mean": 14.5,
                "sd": 4.1833,
                "median": 14.5,
                "min": 8,
                "max": 21
            }
        },
        "factors": [
            {
                "id": 6,
                "common": {
                    "method": "independent_t_test",
                    "cluster_method": {},
                    "t": -6.0622,
                    "p": 0.0001
                },
                "sub": [
                    {
                        "ind": 2,
                        "mean": 11,
                        "sd": 2.1602,
                        "median": 11,
                        "min": 8,
                        "max": 14,
                        "cluster": {}
                    },
                    {
                        "ind": 3,
                        "mean": 18,
                        "sd": 2.1602,
                        "median": 18,
                        "min": 15,
                        "max": 21,
                        "cluster": {}
                    }
                ]
            }
        ]
    },
    {
        "id": 1,
        "common": {
            "is_normal": true,
            "descriptives": {
                "mean": 25,
                "sd": 6.2048,
                "median": 25,
                "min": 15,
                "max": 35
            }
        },
        "factors": [
            {
                "id": 5,
                "common": {
                    "method": "independent_t_test",
                    "cluster_method": {},
                    "t": -6.0622,
                    "p": 0.0001
                },
                "sub": [
                    {
                        "ind": 1,
                        "mean": 18,
                        "sd": 2.1602,
                        "median": 18,
                        "min": 15,
                        "max": 21,
                        "cluster": {}
                    },
                    {
                        "ind": 2,
                        "mean": 25,
                        "sd": 2.1602,
                        "median": 25,
                        "min": 22,
                        "max": 28,
                        "cluster": {}
                    }
                ]
            },
            {
                "id": 6,
                "common": {
                    "method": "one_way_anova",
                    "cluster_method": "Tukey",
                    "t": 73.5,
                    "p": 2.1883e-09
                },
                "sub": [
                    {
                        "ind": 1,
                        "mean": 18,
                        "sd": 2.1602,
                        "median": 18,
                        "min": 15,
                        "max": 21,
                        "cluster": "c"
                    },
                    {
                        "ind": 2,
                        "mean": 25,
                        "sd": 2.1602,
                        "median": 25,
                        "min": 22,
                        "max": 28,
                        "cluster": "b"
                    },
                    {
                        "ind": 3,
                        "mean": 32,
                        "sd": 2.1602,
                        "median": 32,
                        "min": 29,
                        "max": 35,
                        "cluster": "a"
                    }
                ]
            }
        ]
    }
]
```

