server {
        server_name istabot.sadeapp.com;
        root /usr/share/nginx/html;
        index index.html index.htm;

        location / {
                proxy_pass http://127.0.0.1:8000/;
                proxy_redirect off;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location ~ /\.ht
        {
                deny all;
        }
}
