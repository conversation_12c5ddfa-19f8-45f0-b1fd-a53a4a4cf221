# set working directory to this file's location
here::i_am("plumber.R")

# cors filter
source(here::here('v1/generic_helpers/api/cors.R'))

# load packages
source(here::here('v1/generic_helpers/analysis/packages.R'))

# generic helper functions for analysis
source(here::here('v1/generic_helpers/analysis/analysis.R'))

source(here::here('v1/interfaces/multi.R'))
#* Independent Two Groups Test
#* @serializer unboxedJSON
#* @post v1/multi
multi