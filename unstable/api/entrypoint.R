# TODO: This file is not used. It was created to add log mechanism.

# library("plumber")
# library("logger")
# library("here")
# library("tictoc")

# # Create a Plumber router. This will be the object that initializes the API server.
# # plumber.R file contains the API endpoints.
# pr <- plumber::plumb(here::here("plumber.R"))

# # Run the API server.
# pr$run(port = 8000, swagger = TRUE)

# # Log directory path (rcode/unstable/api/log).
# log_dir <- here::here("log")

# # Create log directory if it doesn't exist.
# if (!fs::dir_exists(log_dir)) fs::dir_create(log_dir)

# # Create log. This will be used to log requests and responses.
# log_appender(appender_tee(tempfile("plumber_", log_dir, ".log")))

# pr$registerHooks(
#   list(
#     preroute = function() {
#       # Start timer for log info
#       tictoc::tic()
#     },
#     postroute = function(req, res) {
#       end <- tictoc::toc(quiet = TRUE)
#       # Log details about the request and the response
#       log_info('{convert_empty(req$REMOTE_ADDR)} "{convert_empty(req$HTTP_USER_AGENT)}" {convert_empty(req$HTTP_HOST)} {convert_empty(req$REQUEST_METHOD)} {convert_empty(req$PATH_INFO)} {convert_empty(res$status)} {round(end$toc - end$tic, digits = getOption("digits", 5))}')
#     }
#   )
# )
