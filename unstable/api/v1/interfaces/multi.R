source(here::here("v1/logic/multies/multies.R"))

multi <- function(variable_list, group_list, split_list) {
  multi_validate_params(variable_list, group_list, split_list)

  if (is_split_provided(split_list)) {
    perform_multi_with_split(variable_list, group_list, split_list)
  } else {
    perform_multi(variable_list, group_list)
  }
}

multi_validate_params <- function(variable_list, group_list, split_list) {
  # Validate variable_list
  if(length(variable_list) > 1) {
    for(i in 1:(length(variable_list) - 1)) {
      current_length <- length(variable_list[[i]][[2]])
      next_length <- length(variable_list[[i + 1]][[2]])
  
      if (current_length != next_length) {
        stop("Length of variables should be equal")
      }
    }
  }

  # Validate group_list
  for (group in group_list) {
    for (variable in variable_list) {
      if (length(variable[[2]]) != length(group[[2]])) {
        stop("Length of group data is not equal to length of each variables data in each inset.")
      }
    }
  }

  # Validate split_list
  if (is_split_provided(split_list)) {
    for (variable in variable_list) {
      if (length(variable[[2]]) != length(split_list[[2]])) {
        stop("Length of split data is not equal to length of each variables data in each inset.")
      }
    }
  }
}
