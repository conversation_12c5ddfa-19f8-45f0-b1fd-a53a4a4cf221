is_normal <- function(var, group = NULL) {
  # Perform normality test without group if group is not provided
  if (is.null(group)) {
    # Remove null values from variable if any exists
    if(any(is.na(var))) var <- var[!is.na(var)]

    return(check_normality(var) >= 0.05) # => TRUE/FALSE
  } else {
    # Perform normality test by group if group is provided
    df <- data.frame(var, group)

    # Get rid of null observations for group
    df <- df %>% filter(!is.na(var))

    # Create table of group-wise normality test results
    result <- df %>%
      group_by(group) %>%
      summarize(n = n(), p_val = check_normality(var)) %>%
      ungroup()

    # Check if all groups are normally distributed
    return(all(result$p_val >= 0.05)) # => TRUE/FALSE
  }
}

# Helper: is_normal: Check normality of a variable
check_normality <- function(x) {
  # If all values are identical or there are only 2 observations in a group, forward to Non-parametric methods
  if (var(x) == 0 || length(x) == 2) {
    return(FALSE)
  } else {
    if (length(x) < 50) {
      return(shapiro.test(x)$p.value)
    } else {
      return(lillie.test(x)$p.value)
    }
  }
}

# Helper: For all analyses that require a split, check if split is provided
is_split_provided <- function(split_list) {
  return(!is.null(split_list) && length(split_list) > 0) # => TRUE/FALSE
}
