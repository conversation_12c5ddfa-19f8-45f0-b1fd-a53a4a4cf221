independent_t_test <- function(var, group) {
  result <- t.test(var[[2]] ~ group[[2]], var.equal = is_equal_variances(var, group))

  return(list(
    "method" = "independent_t_test",
    "t" = result$statistic,
    "p" = result$p.value,
    "ci" = list(result$conf.int[1], result$conf.int[2])
  ))
}

# Helper: Use in t-test
is_equal_variances <- function(var, group) {
  return(LeveneTest(var[[2]], factor(group[[2]]), center = mean)$`Pr(>F)`[1] > 0.05)
}