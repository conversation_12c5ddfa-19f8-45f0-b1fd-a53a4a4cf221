# Helper: perform_multi: Decide which test to use and return the test result
test_result <- function(var, group) {
  n_groups <- length(unique(group[[2]]))

  if (n_groups >= 3) {
    test_func <- ifelse(is_normal(var[[2]], group[[2]]),
      one_way_anova,
      kruskall_wallis_h_test
    )
  } else {
    test_func <- ifelse(is_normal(var[[2]], group[[2]]),
      independent_t_test,
      mann_whitney_u_test
    )
  }

  return(test_func(var, group))
}

# Helper: perform_multi: Return descriptives of a variable
variable_descriptives <- function(data) {
  return(list(
    "mean" = mean(data, na.rm = TRUE),
    "sd" = sd(data, na.rm = TRUE),
    "median" = median(data, na.rm = TRUE),
    "min" = min(data, na.rm = TRUE),
    "max" = max(data, na.rm = TRUE)
  ))
}

# Helper: perform_multi: Return descriptives of variable by group
describe_groups <- function(var, group, sub_group_clusters) {
  sub_groups <- sort(unique(group[[2]]))
  table <- descriptives_table(var[[2]], group[[2]])
  response <- vector("list", length = length(sub_groups))

  for (i in seq_along(sub_groups)) {
    response[[i]] <- list(
      "ind" = sub_groups[[i]],
      "mean" = table$mean[i],
      "sd" = table$sd[i],
      "median" = table$median[i],
      "min" = table$min[i],
      "max" = table$max[i],
      "cluster" = sub_group_clusters[[as.character(sub_groups[i])]]
    )
  }

  return(response)
}

# Helper: describe_groups: Return descriptives of a variable by group
descriptives_table <- function(variable, group) {
  df <- data.frame(variable, group)

  return(summarise(
    "group_by"(df, group),
    "mean" = mean(variable),
    "sd" = sd(variable),
    "median" = median(variable),
    "min" = min(variable),
    "max" = max(variable)
  ))
}

# Helper: perform_multi: Clear variable and group considering NA values
clear_multi_data <- function(var, group) {
  df <- data.frame(var=var, group=group)

  df <- df %>% filter(!is.na(var)) %>% # Get rid of NA values in variable for group
        filter(!is.na(group)) %>%      # Get rid of NA values in group
        group_by(group) %>%            # Group by group variable
        filter(n() > 1) %>%            # Performing multi analysis for groups with only 1 observation is not possible.
        ungroup()                      # Remove grouping

  return(df)
}

# Helper: perform_multi_with_split: Filter data by split
multi_filter_data_by_split <- function(variable_list, group_list, split_list) {
  groups <- unique(split_list[[2]])

  results <- list()
  
  for (group in groups) {
    indices <- which(split_list[[2]] == group)
    
    filtered_variable_list <- lapply(variable_list, function(variable) {
      filtered_values <- variable[[2]][indices]

      list(variable[[1]], filtered_values)
    })

    filtered_group_list <- lapply(group_list, function(group) {
      filtered_values <- group[[2]][indices]

      list(group[[1]], filtered_values)
    })

    group_results <- list(
      "sub_ind" = group,
      "variable_list" = filtered_variable_list,
      "group_list" = filtered_group_list
    )

    results <- c(results, list(group_results))
  }
  
  return(results)
}
