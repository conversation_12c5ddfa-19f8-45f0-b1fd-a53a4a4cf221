one_way_anova <- function(var, group) {
  df <- data.frame(column = var[[2]], group = group[[2]])
  cluster_method <- ""

  if (is_equal_variances(var, group)) {
    anova_test <- oneway.test(var[[2]] ~ group[[2]], var.equal = TRUE)
    aov_model <- aov(df$column ~ df$group)
    sample_count <- df %>% count(group[[2]])

    if (anova_test$p.value < 0.05) {
      if (length(unique(sample_count$n)) == 1) {
        multi_compare <- HSD.test(aov_model, "df$group", group = TRUE)$groups
        cluster_method <- "Tukey"
      } else {
        multi_compare <- duncan.test(aov_model, "df$group", group = TRUE)$groups
        cluster_method <- "Duncan"
      }
    } else {
      multi_compare <- NULL
    }

    p_value <- anova_test$p.value
    statistic <- anova_test$statistic
  } else {
    anova_test <- oneway.test(var[[2]] ~ group[[2]], var.equal = FALSE)

    if (anova_test$p.value < 0.05) {
      multi_compare <- tamhaneT2Test(var[[2]], factor(group[[2]]))
      cluster_method <- "Tamhane"
      group_summary <- capture.output(print(summaryGroup(multi_compare)))
      group_count <- length(unique(group[[2]]))
      summary_text <- group_summary[1:(group_count + 1)]
      summary_table <- read.table(text = summary_text, header = TRUE)
      multi_compare <- data.frame(column=summary_table$mean, groups=summary_table$group)
      rownames(multi_compare) <- multi_compare$column
    } else {
      multi_compare <- NULL
    }

    p_value <- anova_test$p.value
    statistic <- anova_test$statistic
  }

  if (!is.null(multi_compare)) {
    if (!is.null(multi_compare$groups)) {
      sub_group_clusters <- setNames(
        multi_compare$groups,
        as.character(rownames(multi_compare))
      )
    } else {
      sub_group_clusters <- list()
    }
  } else {
    sub_group_clusters <- list()
  }

  return(list(
    "method" = "one_way_anova",
    "t" = statistic,
    "p" = p_value,
    "sub_group_clusters" = sub_group_clusters,
    "cluster_method" = cluster_method
  ))
}