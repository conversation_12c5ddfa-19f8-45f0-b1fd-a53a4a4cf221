kruskall_wallis_h_test <- function(var, group) {
  kruskall_wallis_test <- kruskal.test(var[[2]], factor(group[[2]]))
  cluster_method <- ""
  if (kruskall_wallis_test$p.value < 0.05) {
    DT <- dunnTest(var[[2]] ~ factor(group[[2]]), method = "bonferroni")
    PT <- DT$res
    multi_compare <- cldList(P.adj ~ Comparison, data = PT, threshold = 0.05, remove.zero = FALSE)
    cluster_method <- "Dunn"
  } else {
    multi_compare <- NULL
  }
  sub_group_clusters <- if (!is.null(multi_compare) && !is.null(multi_compare$Group)) {
    setNames(multi_compare$Letter, as.character(multi_compare$Group))
  } else {
    list()
  }

  list(
    "method" = "kruskall_wallis_h_test",
    "t" = kruskall_wallis_test$statistic,
    "p_value" = kruskall_wallis_test$p.value,
    "sub_group_clusters" = sub_group_clusters,
    "cluster_method" = cluster_method
  )
}