source(here::here("v1/logic/multies/analysis/helper.R"))
source(here::here("v1/logic/multies/analysis/independent_t_test.R"))
source(here::here("v1/logic/multies/analysis/mann_whitney_u_test.R"))
source(here::here("v1/logic/multies/analysis/one_way_anova_test.R"))
source(here::here("v1/logic/multies/analysis/kruskall_wallis_h_test.R"))

perform_multi_with_split <- function(variable_list, group_list, split_list) {
  # Split data by split variable.
  splitted_params <- multi_filter_data_by_split(variable_list, group_list, split_list)

  # Perform multi analysis for each splitted data.
  stats_result <- map(splitted_params, function(params) {
    list(
      "ind" = params$sub_ind,
      "stats" = perform_multi(params$variable_list, params$group_list))
  })

  # Filter out if stats result exist but empty for splitted data.
  stats_result <- Filter(function(x) length(x$stats) > 0, stats_result)

  return(list("split_id" = split_list[[1]], "sub" = stats_result))
}

perform_multi <- function(variable_list, group_list) {
  # Initialize response array which stores comparison results for each variable by group.
  response <- vector("list", length = length(variable_list))

  for (j in seq_along(variable_list)) {
    var <- variable_list[[j]]
    common <- list(
      "is_normal" = is_normal(var[[2]]),
      "descriptives" = variable_descriptives(var[[2]])
    )

    # Initialize factors array which stores comparison results for each group.
    factors <- list()

    for (i in seq_along(group_list)) {
      # Initialize variable and group values.
      var <- variable_list[[j]]
      group <- group_list[[i]]

      # Get rid of missing values and insufficient groups.
      clean_data <- clear_multi_data(var[[2]], group[[2]])

      # Update variable and group with clean data.
      var[[2]] = clean_data$var; group[[2]] = clean_data$group

      # Skip if there are not enough groups for multi analysis.
      if(length(unique(group[[2]])) < 2) {
        next
      }

      # Perform test for variable by group
      test_result <- test_result(var, group)

      # Add comparison result for group to factors array.
      factors[[length(factors) + 1]] <- list(
        id = group[[1]],
        common = list(
          "method" = test_result$method,
          "cluster_method" = test_result$cluster_method,
          "t" = test_result$t,
          "p" = test_result$p
        ),
        sub = describe_groups(var, group, test_result$sub_group_clusters)
      )
    }

    # Add comparison results for variable to response array if there are any.
    if (length(factors) > 0) {
      response[[j]] <- list(id = var[[1]], common = common, factors = factors)
    }
  }

  # Filter out empty hashes in response array.
  response <- response[!sapply(response, is.null)]

  return(response)
}
