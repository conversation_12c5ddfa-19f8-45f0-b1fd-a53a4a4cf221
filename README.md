# Installation Notes
## Essentials
```bash
apt-get install build-essential libcurl4-openssl-dev cmake libsodium-dev libssl-dev libfontconfig1-dev libharfbuzz-dev libxml2-dev libfribidi-dev libfreetype6-dev libpng-dev libtiff5-dev libjpeg-dev libblas-dev liblapack-dev libgmp-dev libmpfr-dev gfortran
```
## Install R
[How To Install R on Ubuntu 22.04|DigitalOcean](https://www.digitalocean.com/community/tutorials/how-to-install-r-on-ubuntu-22-04)
## Libraries
### CRAN
```r
install.packages("https://cran.r-project.org/src/contrib/Archive/mvtnorm/mvtnorm_1.0-8.tar.gz", repos=NULL)
```
### Dependencies
```r
packages_to_install <- c('ggpubr','dplyr','rstatix','rcompanion','multcompView','lme4','ez','rcompanion','stats','FSA','nortest','DescTools',
                         'tidyverse','nortest','DescTools','PMCMRplus','dplyr','lme4','DescTools','agricolae','jsonlite','rcompanion','FSA',
                         'psych', 'logger', 'here', 'hash')
install.packages(packages_to_install)
```
> Installing packages may take a few minutes.
### Plumber
```r
install.packages("plumber")
```
## Run Plumber
```r
library(plumber); r <- plumb('/path/to/your/plumber.R'); r$run(port = 8000,swagger=TRUE)
```

## Hot Reload in Development Mode: Drip
[Drip Github Page](https://github.com/siegerts/drip)
---

# Server Settings
## Allow firewall to connect via ssh
```bash
ufw enable && ufw allow ssh
```
## Setting up the plumber service with systemd
```bash
cp config/service/plumber.service /etc/systemd/system &&
ln -s /etc/systemd/system/plumber.service /etc/systemd/system/multi-user.target.wants/
```
## Nginx
### Install Nginx
[How To Install Nginx on Ubuntu 22.04|DigitalOcean](https://www.digitalocean.com/community/tutorials/how-to-install-nginx-on-ubuntu-22-04)
### Configure Nginx
```bash
cp /config/nginx/api /etc/nginx/sites-available &&
ln -s /etc/nginx/sites-available/api /etc/nginx/sites-enabled/
```
# Statistical Tests
## Parametric Methods
- One Sample T
- Independent Samples T
- Paired Samples T
- ANOVA
- Repeated Measures ANOVA
- Pearson Correlation
- Point-biserial Correlation
- Tetrachoric Correlation
## Non-parametric Methods
- Wilcoxon
- Runs
- Mann-Whitney U
- Kruskal Wallis
- Friedman
- Chi Square
- Spearman Correlation
